<app-header (sideOpen)="toggleSidebar($event)"
    [isSidebarOpen]="isSidebarOpen"></app-header>
<div class="header_stunt"></div>

<app-toaster></app-toaster>
<dialog class="stylized_ relative" #modal>
    <div (click)="closeModal(modal)" class="x_bt"></div>
    <div class="flex flex-col">
        <p class="auth_head ml-auto mr-auto">
            Намасте!
        </p>
        <p class="auth_p">
            {{message}}
        </p>
    </div>
    <div class="nams_wrap"></div>
    <div class="b_wrap">
        <button [routerLink]="['/ru/signup']" (click)="closeModal(modal)" class="else_bt reg_bt">Регистрация</button>
        <button [routerLink]="['/ru/signin']" (click)="closeModal(modal)" class="else_bt">Вход</button>
        <button (click)="closeModal(modal)" class="else_bt">Отмена</button>
    </div>
</dialog>
@if(isSidebarOpen) {
<div class="side_blbc"></div>
}
@if(isThemeSwitchClicked) {
<div class="ent_choose_blc thm_">
    <div class="ent-mob_switch">
        <div (click)="closeSide()" class="x_bt"></div>
        <button [ngClass]="{'selected_b': theme == 'Light'}" class="sel_btn lt_set"
            (click)="onThemeChange('Light')">Светлая</button>
        <button [ngClass]="{'selected_b': theme == 'lightBlue'}" class="sel_btn bl_set"
            (click)="onThemeChange('lightBlue')">Голубая</button>
        <!-- <button [ngClass]="{'selected_b': theme == 'Dark'}" class="sel_btn dk_set"
            (click)="onThemeChange('Dark')">Темная</button> -->
    </div>
</div>
}
@if(isLangSwitchClicked) {
<div class="ent_choose_blc">
    <div class="ent-mob_switch">
        <div (click)="isLangSwitchClicked = false" class="x_bt"></div>
        <button [ngClass]="{'selected_b': currentLanguage() == 'ru'}" class="sel_btn"
            (click)="changeLanguage('ru')">RU</button>
        <button [ngClass]="{'selected_b': currentLanguage() == 'en'}" class="sel_btn"
            (click)="changeLanguage('en')">EN</button>
        <button [ngClass]="{'selected_b': currentLanguage() == 'de'}" class="sel_btn"
            (click)="changeLanguage('de')">DE</button>
    </div>
</div>
}
<div id="sidebar" appClickOutside (clickOutside)="isSidebarOpen = false;setBody();"
    [ngClass]="{'active': isSidebarOpen}" class="sidebar_cl relative">
    <div class="side_head">
        <a [routerLink]="[profileService.name() ? '/ru/profile' : '/ru/signin']"
            (click)="isSidebarOpen = false;setBody();$event.stopPropagation();" class="user-menu spec_">
            <div class="relative cursor-pointer">
                <div class="notifications">
                    @if (profileService.profile) {
                    <div class="user_logo_ head_section authorized_">
                        @if (profileService.profile && profileService.profile.avatar && profileService.profile.avatar.name) {
                        <img ngSrc="{{environment.serverUrl + '/upload/' + profileService.avatar()}}" width="36"
                            height="36" loading="lazy" alt="logo">
                        } @else {
                        <img ngSrc="assets/images/user_logo.webp" width="36" height="36" loading="lazy" alt="logo">
                        }
                    </div>
                    <!-- <span>1</span> -->
                    } @else {
                    <div class="user_logo_ head_section"></div>
                    }
                </div>
            </div>
            <div class="user-dropdown app_section" [ngClass]="{'has-arrow': profileService.name()}">{{
                profileService.name() === "null" ? '' : (profileService.name() || 'Войти') }}
            </div>
        </a>
        <div [routerLink]="['/']" (click)="isSidebarOpen = false;setBody();$event.stopPropagation();" class="center_logo"></div>
        <div class="rh_block">
            <div class="theme_choose">
                <div (click)="isThemeSwitchClicked = !isThemeSwitchClicked" [ngClass]="theme" class="them_cn"></div>
                <div class="lan_switch">
                    <button [ngClass]="{'selected_b': theme == 'Light'}" class="Light"
                        (click)="onThemeChange('Light')">Светлая</button>
                    <button [ngClass]="{'selected_b': theme == 'lightBlue'}" class="lightBlue"
                        (click)="onThemeChange('lightBlue')">Голубая</button>
                    <!-- <button [ngClass]="{'selected_b': theme == 'Dark'}" class="Dark"
                        (click)="onThemeChange('Dark')">Темная</button> -->
                </div>
            </div>
            <div class="lan_wrap">
                <div (click)="isLangSwitchClicked = !isLangSwitchClicked" class="lan_btn">
                    <p>
                        {{currentLanguage()}}
                    </p>
                </div>
                <div class="lan_switch">
                    <button [ngClass]="{'selected_b': currentLanguage() == 'ru'}" class="def-selection"
                        (click)="changeLanguage('ru')">RU</button>
                    <button [ngClass]="{'selected_b': currentLanguage() == 'en'}" class="def-selection"
                        (click)="changeLanguage('en')">EN</button>
                    <button [ngClass]="{'selected_b': currentLanguage() == 'de'}" class="def-selection"
                        (click)="changeLanguage('de')">DE</button>
                  <button [ngClass]="{'selected_b': currentLanguage() == 'ua'}" class="def-selection"
                          (click)="changeLanguage('ua')">UA</button>
                  <button [ngClass]="{'selected_b': currentLanguage() == 'it'}" class="def-selection"
                          (click)="changeLanguage('it')">IT</button>
                </div>
            </div>
            <div class="flex">
                <div class="close_butn" (click)="isSidebarOpen = false;setBody();"></div>
            </div>
        </div>
    </div>
    <div class="flex flex-col list_menu">
        <div (click)="openMenu(0);isSidebarOpen = true;setBody();" class="flex item_menu">
            <div [ngClass]="{'rotate': isMenuOpened[0]}" class="img_row">
                <div class="text_c">События</div>
            </div>
            @if(isMenuOpened[0]) {
            <div class="flex flex-col ms_wrap">
                <ul>
                    <li>Радио</li>
                    <li>Фото</li>
                </ul>
            </div>
            }
        </div>
        <div (click)="openMenu(1);isSidebarOpen = true;setBody();" class="flex item_menu">
            <div [ngClass]="{'rotate': isMenuOpened[1]}" class="img_row">
                <div class="text_c">Курсы</div>
            </div>
            @if(isMenuOpened[1]) {
            <div class="flex flex-col ms_wrap">
                <ul>
                    <li (click)="navigateToCategorie()">Курсы</li>
                </ul>
            </div>
            }
        </div>
        <div (click)="openMenu(2);isSidebarOpen = true;setBody();" class="flex item_menu">
            <div [ngClass]="{'rotate': isMenuOpened[2]}" class="img_row">
                <div (click)="navigateToLibrary();" class="text_c">Библиотека</div>
            </div>
            @if(isMenuOpened[2]) {
            <div class="flex flex-col ms_wrap">
                <ul>
                    <li>Радио</li>
                    <li>Фото</li>
                </ul>
            </div>
            }
        </div>
        <div (click)="openMenu(3);isSidebarOpen = true;setBody();" class="flex item_menu">
            <div [ngClass]="{'rotate': isMenuOpened[3]}" class="img_row">
                <div class="text_c">Медиа</div>
            </div>
            @if(isMenuOpened[3]) {
            <div class="flex flex-col ms_wrap">
                <ul>
                    <li (click)="navigateToAudio();">Лекции</li>
                    <li>Аудиокниги</li>
                    <li>Бхаджаны</li>
                    <li>Музыка</li>
                    <li>Радио</li>
                    <li (click)="navigateToPhoto();">Фото</li>
                </ul>
            </div>
            }
        </div>
        <div (click)="openMenu(4);isSidebarOpen = true;setBody();" class="flex item_menu">
            <div [ngClass]="{'rotate': isMenuOpened[4]}" class="img_row">
                <div class="text_c">Традиция</div>
            </div>
            @if(isMenuOpened[4]) {
            <div class="flex flex-col ms_wrap">
                <ul>
                    <li>Радио</li>
                    <li>Фото</li>
                </ul>
            </div>
            }
        </div>
        <div (click)="openMenu(5);isSidebarOpen = true;setBody();" class="flex item_menu">
            <div [ngClass]="{'rotate': isMenuOpened[5]}" class="img_row">
                <div class="text_c">Практика</div>
            </div>
            @if(isMenuOpened[5]) {
            <div class="flex flex-col ms_wrap">
                <ul>
                    <li>Радио</li>
                    <li>Фото</li>
                </ul>
            </div>
            }
        </div>
        <div (click)="openMenu(6);isSidebarOpen = true;setBody();" class="flex item_menu">
            <div [ngClass]="{'rotate': isMenuOpened[6]}" class="img_row">
                <div (click)="navigateToForum();" class="text_c">Форум</div>
            </div>
            @if(isMenuOpened[6]) {
            <div class="flex flex-col ms_wrap">
                <ul>
                    <li>Радио</li>
                    <li>Фото</li>
                </ul>
            </div>
            }
        </div>
        <!-- <div class="item_menu">
            <a [routerLink]="['/', currentLanguage(), 'search']">Поиск</a>
        </div>

        <div class="item_menu">
            <a [routerLink]="['/', currentLanguage(), 'sitemap']" (click)="openSitemap()">Карта сайта</a>
        </div> -->
    </div>
    <div class="flex side_">
        @if (authService.token()) {
            <div class="button_img">
                <a [routerLink]="['/ru/signin']" (click)="logout()">
                    <button>Выйти</button>
                </a>
            </div>
        }
        @if (!authService.token()) {
            <div class="button_img">
                <a [routerLink]="['/ru/signin']">
                    <button>Войти</button>
                </a>
            </div>
            <div class="button_img reg">
                <a [routerLink]="['/ru/signup']">
                    <button>Регистрация</button>
                </a>
            </div>
        }
    </div>
    <div class="social_side_">
        <div class="soc_ tel_"></div>
        <div class="soc_ yt_"></div>
        <div class="soc_ vk_"></div>
        <div class="soc_ fb_"></div>
        <div class="soc_ in_"></div>
    </div>
</div>
<div class="content-height_wrap">
    <router-outlet />
</div>

<app-main-player (sideOpen)="toggleSidebar($event)"></app-main-player>
<!-- <app-additional-mobile-menu (sideOpen)="toggleSidebar($event)"></app-additional-mobile-menu> -->
<app-footer></app-footer>

<!-- Global Ad Dialog -->
<app-ad-dialog></app-ad-dialog>
