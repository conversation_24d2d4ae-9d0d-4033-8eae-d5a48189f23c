<breadcrumb></breadcrumb>

<div class="container">
  <div class="news-page">
    <div class="advertisements-list">
      <div *ngIf="advertisings.length > 0" class="ads-grid">
        <div *ngFor="let ad of advertisings" class="ad-card" (click)="navigateToLink(ad.link)">
          <div class="ad-image" *ngIf="ad.image">
            <img [src]="environment.serverUrl + '/upload/' + ad.image.path" [alt]="ad.title">
          </div>
          <div class="ad-content">
            <h3 class="ad-title">{{ ad.title }}</h3>
            <p class="ad-description">{{ ad.description }}</p>
            <div class="ad-date">{{ ad.date | date:'mediumDate' }}</div>
            <a [href]="ad.link" target="_blank" class="ad-link">Ссылка</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


