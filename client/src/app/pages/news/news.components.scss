

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.news-page {
  h1 {
    margin-bottom: 30px;
    font-size: 2rem;
  }

  h2 {
    margin-bottom: 20px;
    font-size: 1.5rem;
  }
}

.advertisements-list {
  margin-top: 30px;
}

.loading,
.no-items {
  text-align: center;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.ads-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.ad-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: #fff;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
}



.ad-content {
  display: flex;
}

.ad-title {
  font-size: 1.2rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.ad-description {
  color: #666;
  margin-bottom: 15px;
  line-height: 1.5;
}

.ad-date {
  color: #999;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.ad-link {
  display: inline-block;
  color: #3498db;
  text-decoration: none;
  font-weight: 500;

  &:hover {
    text-decoration: underline;
  }
}

@media (max-width: 768px) {
  .ads-grid {
    grid-template-columns: 1fr;
  }
}